// fake_hbm3_pc.h
#pragma once

#include <cstdint>
#include <unordered_map>
#include <vector>
#include <mutex>
#include "svdpi.h"


class FakeHBM3PC {
public:
    static FakeHBM3PC& getInstance();
    ~FakeHBM3PC();

    // DPI-C interface functions
    void pcWrite(uint16_t bfm_id,
                uint64_t addr, const svBitVecVal* data, 
                const svBitVecVal* mask);
                
    void pcRead(uint16_t bfm_id,
               uint64_t addr, svBitVecVal* rdata);
    void reset();

private:
    FakeHBM3PC();
    FakeHBM3PC(const FakeHBM3PC&) = delete;
    FakeHBM3PC& operator=(const FakeHBM3PC&) = delete;

    // Memory storage
    struct MemoryKey {
        uint16_t bfm_id;
        uint64_t addr;

        bool operator==(const MemoryKey& other) const {
            return bfm_id == other.bfm_id &&
                   addr == other.addr;
        }
    };

    struct MemoryKeyHash {
        // Combines two hash values into one.
        static size_t CombineHash(size_t lhs, size_t rhs) {
            // This is the standard way to combine hashes in C++17.
            // 0x9e3779b9 is the golden ratio, helps to spread bits.
            return lhs ^ (rhs + 0x9e3779b9 + (lhs << 6) + (lhs >> 2));
        }

        size_t operator()(const MemoryKey& key) const {
            // Hash each field separately, then combine.
            size_t h1 = std::hash<uint16_t>{}(key.bfm_id);
            size_t h2 = std::hash<uint64_t>{}(key.addr);
            return CombineHash(h1, h2);
        }
    };

    std::unordered_map<MemoryKey, std::vector<uint8_t>, MemoryKeyHash> memory_;
    std::mutex memory_mutex_;
};
// test_fake_hbm3_mm_bfm.cc for blocking BFM
#include "fake_hbm3_mm_bfm.h"
#include <gtest/gtest.h>
#include <vector>
#include <cstring>

// This test suite is for the blocking version of the FakeHBM3PC.
// It directly calls the C++ methods of the FakeHBM3PC singleton.
// No DPI-C callbacks (like s2h_hbm3_read_data_reply) are needed,
// as the pcRead function is blocking and returns data directly.

class FakeHBM3PCTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Reset the fake HBM3PC instance before each test
        FakeHBM3PC::getInstance().reset();
        
        // Initialize test data (256 bits / 32 bytes)
        test_data_.resize(32);
        for (size_t i = 0; i < 32; ++i) {
            test_data_[i] = i & 0xFF;
        }
    }

    std::vector<uint8_t> test_data_;
};

// Test basic write operation followed by a read operation.
TEST_F(FakeHBM3PCTest, BasicWriteRead) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0x1000;
    
    // Prepare a full mask (32 bits for 256-bit data)
    std::vector<uint8_t> full_mask(4, 0xFF); 

    // Write data
    hbm.pcWrite(0, test_addr, 
                reinterpret_cast<svBitVecVal*>(test_data_.data()),
                256,
                reinterpret_cast<svBitVecVal*>(full_mask.data()));

    // Read data back using the blocking pcRead
    std::vector<uint8_t> read_data(32, 0);
    hbm.pcRead(0, test_addr, reinterpret_cast<svBitVecVal*>(read_data.data()));
    
    // Verify that the read data matches the written data
    EXPECT_EQ(read_data, test_data_) << "Received data does not match test data";
}

// Test writing to and reading from multiple addresses.
TEST_F(FakeHBM3PCTest, MultipleReads) {
    auto& hbm = FakeHBM3PC::getInstance();
    const int NUM_REQUESTS = 5;
    std::vector<uint8_t> full_mask(4, 0xFF);
    
    // Write different patterns to different addresses
    for (int i = 0; i < NUM_REQUESTS; ++i) {
        std::vector<uint8_t> pattern(32, i);
        uint64_t addr = 0x1000 * (i + 1);
        hbm.pcWrite(0, addr,
                    reinterpret_cast<svBitVecVal*>(pattern.data()),
                    256,
                    reinterpret_cast<svBitVecVal*>(full_mask.data()));
    }
    
    // Read and verify each address
    for (int i = 0; i < NUM_REQUESTS; ++i) {
        std::vector<uint8_t> expected_pattern(32, i);
        uint64_t addr = 0x1000 * (i + 1);
        
        std::vector<uint8_t> read_data(32, 0);
        hbm.pcRead(0, addr, reinterpret_cast<svBitVecVal*>(read_data.data()));
        
        EXPECT_EQ(read_data, expected_pattern) 
            << "Data mismatch for request " << i;
    }
}

// Test masked writes, where only part of the data is written.
TEST_F(FakeHBM3PCTest, MaskedWrite) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0x2000;
    
    // Create a mask to write only the first 16 bytes.
    // The mask is 32 bits, where each bit corresponds to a byte.
    // 0xFFFF0000 -> {0xFF, 0xFF, 0x00, 0x00}
    std::vector<uint8_t> half_mask = {0xFF, 0xFF, 0x00, 0x00}; 
    
    hbm.pcWrite(0, test_addr,
                reinterpret_cast<svBitVecVal*>(test_data_.data()),
                256,
                reinterpret_cast<svBitVecVal*>(half_mask.data()));
    
    // Read back and verify
    std::vector<uint8_t> read_data(32, 0);
    hbm.pcRead(0, test_addr, reinterpret_cast<svBitVecVal*>(read_data.data()));
    
    // First 16 bytes should match test data
    for (size_t i = 0; i < 16; ++i) {
        EXPECT_EQ(read_data[i], test_data_[i]) 
            << "Mismatch at byte " << i;
    }
    // Last 16 bytes should be zero, as they were masked
    for (size_t i = 16; i < 32; ++i) {
        EXPECT_EQ(read_data[i], 0) 
            << "Non-zero at byte " << i << " which should have been masked";
    }
}

// Test that a read after a write returns the most recent data.
TEST_F(FakeHBM3PCTest, ReadAfterWrite) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0x3000;
    std::vector<uint8_t> full_mask(4, 0xFF);
    
    // Initial write with pattern 0xAA
    std::vector<uint8_t> initial_data(32, 0xAA);
    hbm.pcWrite(0, test_addr,
                reinterpret_cast<svBitVecVal*>(initial_data.data()),
                256,
                reinterpret_cast<svBitVecVal*>(full_mask.data()));

    // Read back and check
    std::vector<uint8_t> read_data1(32, 0);
    hbm.pcRead(0, test_addr, reinterpret_cast<svBitVecVal*>(read_data1.data()));
    EXPECT_EQ(read_data1, initial_data);

    // Write new pattern 0xBB to the same address
    std::vector<uint8_t> new_data(32, 0xBB);
    hbm.pcWrite(0, test_addr,
                reinterpret_cast<svBitVecVal*>(new_data.data()),
                256,
                reinterpret_cast<svBitVecVal*>(full_mask.data()));
    
    // Read back and check for the new data
    std::vector<uint8_t> read_data2(32, 0);
    hbm.pcRead(0, test_addr, reinterpret_cast<svBitVecVal*>(read_data2.data()));
    EXPECT_EQ(read_data2, new_data);
}

// Test that reading from an address that has not been written to returns all zeros.
TEST_F(FakeHBM3PCTest, ReadUnwrittenAddress) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0xDEADBEEF;

    // Pre-fill read buffer with non-zero values to ensure it gets overwritten.
    std::vector<uint8_t> read_data(32, 0xCC); 
    hbm.pcRead(0, test_addr, reinterpret_cast<svBitVecVal*>(read_data.data()));

    // Expect to read all zeros
    std::vector<uint8_t> expected_data(32, 0);
    EXPECT_EQ(read_data, expected_data) << "Reading from unwritten address should return zeros";
}

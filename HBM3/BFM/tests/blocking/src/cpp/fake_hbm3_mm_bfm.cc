// fake_hbm3_pc.cc
#include "fake_hbm3_mm_bfm.h"
#include "svdpi.h"
#include <cstring>
#include <unordered_map>
#include <vector>
#include <algorithm>

#ifdef NO_SIMULATOR
#define vpi_printf printf
#else
#include <vpi_user.h>
#endif

std::unordered_map<uint16_t, uint32_t> bfm_width_map;

extern "C" {
    void h2s_hbm3_write(svBitVecVal* bfm_id, svBitVecVal* addr,
                       svBitVecVal* data,
                       svBitVecVal* mask) {
        uint64_t address = *reinterpret_cast<uint64_t*>(addr);
        uint16_t id = *(uint16_t*)bfm_id;
        uint32_t data_size = bfm_width_map[id];
        uint32_t data_size_in_bytes = (data_size+7)/8;
        
        // Debug print to show what we're receiving
        vpi_printf("[h2s_hbm3_write] - bfm_id: %d, addr: %lx\n", id, address);
        vpi_printf("[h2s_hbm3_write] - First few data bytes: ");
        const uint8_t* data_bytes = reinterpret_cast<const uint8_t*>(data);
        for (int i = 0; i < data_size_in_bytes; i++) {
            vpi_printf("%02x ", data_bytes[i]);
        }
        vpi_printf("\n");
        
        vpi_printf("[h2s_hbm3_write] - First few mask bytes: ");
        const uint8_t* mask_bytes = reinterpret_cast<const uint8_t*>(mask);
        for (int i = 0; i < (data_size_in_bytes +7)/8; i++) {
            vpi_printf("%02x ", mask_bytes[i]);
        }
        vpi_printf("\n");
        
        FakeHBM3PC::getInstance().pcWrite(id, address, data, mask);
    }

    void h2s_hbm3_read(svBitVecVal* bfm_id, svBitVecVal* addr,
                     svBitVecVal* rdata) {
        uint64_t address = *reinterpret_cast<uint64_t*>(addr);
        FakeHBM3PC::getInstance().pcRead(*(uint16_t*)bfm_id, address, rdata);
    }

    void h2s_hbm3_init_bfm(svBitVecVal* bfm_id, int unsigned data_width) {
        // This function is kept for compatibility with the SV BFM,
        // but it does not need to do anything in the blocking implementation.
        bfm_width_map[*(uint16_t*)bfm_id] = data_width;
        vpi_printf("[init_bfm] - bfm_id: %d, data_width: %d\n", *(uint16_t*)bfm_id, data_width);
    }
}


FakeHBM3PC& FakeHBM3PC::getInstance() {
    static FakeHBM3PC instance;
    return instance;
}

FakeHBM3PC::FakeHBM3PC() {}

FakeHBM3PC::~FakeHBM3PC() {}

void FakeHBM3PC::pcWrite(uint16_t bfm_id,
                         uint64_t addr, const svBitVecVal* data,
                         const svBitVecVal* mask) {
    std::lock_guard<std::mutex> lock(memory_mutex_);
    
    int data_size = bfm_width_map[bfm_id];
    int byte_size = (data_size + 7) / 8;
    vpi_printf("[pcWrite] - bfm_id: %d, addr: %lx, data_size: %d, byte_size: %d\n",
               bfm_id, addr, data_size, byte_size);
    
    // Debug: Print mask bytes
    const uint8_t* mask_bytes = reinterpret_cast<const uint8_t*>(mask);
    vpi_printf("[pcWrite] - DEBUG: Mask bytes: ");
    for (uint32_t i = 0; i < (byte_size + 7) / 8; ++i) {
        vpi_printf("%02x ", mask_bytes[i]);
    }
    vpi_printf("\n");
    
    // Debug: Print first few data bytes
    const uint8_t* data_bytes = reinterpret_cast<const uint8_t*>(data);
    vpi_printf("[pcWrite] - DEBUG: First few data bytes: ");
    for (uint32_t i = 0; i < std::min(byte_size, 16); ++i) {
        vpi_printf("%02x ", data_bytes[i]);
    }
    vpi_printf("\n");
    
    // Write each byte to its own address
    int bytes_written = 0;
    for (uint32_t i = 0; i < byte_size; ++i) {
        // Check if this byte's bit is set in the mask
        // Each bit in the mask corresponds to a byte in the data
        if (mask_bytes[i / 8] & (1 << (i % 8))) {
            MemoryKey key{bfm_id, addr + i};  // Create a key for each byte address
            auto& mem_data = memory_[key];
            if (mem_data.empty()) {
                mem_data.resize(1, 0);  // Only need 1 byte per address
            }
            mem_data[0] = data_bytes[i];
            vpi_printf("[pcWrite] - bfm_id: %d, addr: %lx, data: %02x\n",
                   bfm_id, addr+i, data_bytes[i]);
            bytes_written++;
        }
    }
    
    vpi_printf("[pcWrite] - Total bytes written: %d\n", bytes_written);
}

void FakeHBM3PC::pcRead(uint16_t bfm_id,
                          uint64_t addr, svBitVecVal* rdata) {
    std::lock_guard<std::mutex> lock(memory_mutex_);
    int data_size = bfm_width_map[bfm_id];
    int byte_size = (data_size + 7) / 8;
    
    // Clear the output buffer first
    memset(rdata, 0, byte_size);
    uint8_t* rdata_bytes = reinterpret_cast<uint8_t*>(rdata);
    
    bool any_data_found = false;
    vpi_printf("[pcRead] - bfm_id: %d, addr: %lx, data_size: %d, byte_size: %d\n",
               bfm_id, addr, data_size, byte_size);
    
    // Read each byte from its own address
    for (uint32_t i = 0; i < byte_size; ++i) {
        MemoryKey key{bfm_id, addr + i};
        auto it = memory_.find(key);
        if (it != memory_.end()) {
            const auto& mem_data = it->second;
            if (!mem_data.empty()) {
                rdata_bytes[i] = mem_data[0];  // Get the single byte stored at this address
                any_data_found = true;
                vpi_printf("[pcRead] - bfm_id: %d, addr: %lx, byte[%d]: %x\n",
                           bfm_id, addr+i, i, rdata_bytes[i]);
            }
        }
    }
    
    if (any_data_found) {
        vpi_printf("[pcRead] - bfm_id: %d, addr: %lx, data found\n", bfm_id, addr);
    } else {
        vpi_printf("[pcRead] - bfm_id: %d, addr: %lx, no data found\n", bfm_id, addr);
    }
}

void FakeHBM3PC::reset() {
    std::lock_guard<std::mutex> lock(memory_mutex_);
    memory_.clear();
}

cmake_minimum_required(VERSION 3.10)
project(fake_hbm3_mm_bfm_test)

# Set default build type to Debug
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Add debug flags
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DNO_SIMULATOR")

# Add Google Test directory
set(GTEST_ROOT /home/<USER>/gtest)
set(GTEST_INCLUDE_DIR ${GTEST_ROOT}/include)
set(GTEST_LIBRARY_DIR ${GTEST_ROOT}/lib64)

# Add VCS directory
set(VCS_HOME /opt/synopsys/vcs/V-2023.12-SP2)
set(VCS_INCLUDE_DIR ${VCS_HOME}/include)

# Include directories
include_directories(${GTEST_INCLUDE_DIR} 
    ${VCS_INCLUDE_DIR})
link_directories(${GTEST_LIBRARY_DIR})

# Add test executable
add_executable(fake_hbm3_mm_bfm_test
    test_fake_hbm3_mm_bfm.cc
    fake_hbm3_mm_bfm.cc
)

# Link against GTest and pthread
target_link_libraries(fake_hbm3_mm_bfm_test
    gtest
    gtest_main
    pthread
)

# Add compile options
target_compile_features(fake_hbm3_mm_bfm_test PRIVATE cxx_std_17)

# Print build type
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")

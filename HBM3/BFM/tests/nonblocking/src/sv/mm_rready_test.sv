// Test for mm_rready functionality in hbm3_mm_bfm
// This test verifies that read data is only returned when mm_rready is asserted

module hbm3_mm_tb;
    parameter ADDR_WIDTH = 29;
    parameter DATA_WIDTH = 256;
    parameter DATA_BYTE_WIDTH = DATA_WIDTH/8;
    parameter CLK_PERIOD = 10;

    // Common signals
    logic mm_clk;
    logic mm_rstn;

    // BFM signals
    logic                           mm_wen;
    logic [ADDR_WIDTH-1:0]         mm_waddr;
    logic [DATA_WIDTH-1:0]         mm_wdata;
    logic [DATA_BYTE_WIDTH-1:0]    mm_wmask;
    
    logic                           mm_ren;
    logic [ADDR_WIDTH-1:0]         mm_raddr;
    logic                           mm_rready;
    logic [DATA_WIDTH-1:0]         mm_rdata;
    logic                           mm_rvalid;

    // Clock generation
    initial begin
        mm_clk = 0;
        forever #(CLK_PERIOD/2) mm_clk = ~mm_clk;
    end

    // Reset and signal initialization
    initial begin
        mm_rstn = 0;
        mm_wen = 0;
        mm_ren = 0;
        mm_rready = 0;  // 初始设置为低电平
        mm_waddr = 0;
        mm_wdata = 0;
        mm_wmask = 0;
        mm_raddr = 0;
        
        #(CLK_PERIOD * 10);
        mm_rstn = 1;
        #(CLK_PERIOD * 5);
        
        // Test sequence
        test_mm_rready_control();
        
        #(CLK_PERIOD * 20);
        $display("Test completed");
        $finish;
    end

    // Instantiate BFM for blocking mode test
    hbm3_mm_bfm #(
        .HBM_ID(0),
        .CHANNEL_ID(0),
        .PC_ID(0),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .DATA_BYTE_WIDTH(DATA_BYTE_WIDTH)
    ) dut (
        .mm_clk(mm_clk),
        .mm_rstn(mm_rstn),
        .mm_wen(mm_wen),
        .mm_waddr(mm_waddr),
        .mm_wdata(mm_wdata),
        .mm_wmask(mm_wmask),
        .mm_ren(mm_ren),
        .mm_raddr(mm_raddr),
        .mm_rready(mm_rready),
        .mm_rdata(mm_rdata),
        .mm_rvalid(mm_rvalid),
        .mm_rlat(32'd100)
    );

    // Test task
    task test_mm_rready_control();
        logic [DATA_WIDTH-1:0] test_data [0:3];
        int received_count;
        logic [DATA_WIDTH-1:0] received_data [0:3];
        int cont_count;
        int i;

        // 准备测试数据
        test_data[0] = 256'hDEADBEEF_CAFEBABE_12345678_9ABCDEF0_FEDCBA09_87654321_ABCDEF01_23456789;
        test_data[1] = 256'h11111111_22222222_33333333_44444444_55555555_66666666_77777777_88888888;
        test_data[2] = 256'hAAAAAAAA_BBBBBBBB_CCCCCCCC_DDDDDDDD_EEEEEEEE_FFFFFFFF_00000000_11111111;
        test_data[3] = 256'h12345678_9ABCDEF0_FEDCBA09_87654321_ABCDEF01_23456789_DEADBEEF_CAFEBABE;

        $display("=== Testing mm_rready FIFO functionality ===");

        // Step 1: Write test data to different addresses
        $display("Step 1: Writing test data to multiple addresses");
        for (int i = 0; i < 4; i++) begin
            @(posedge mm_clk);
            mm_wen <= 1'b1;
            mm_waddr <= 29'h1000 + (i * 32);  // Different addresses
            mm_wdata <= test_data[i];
            mm_wmask <= {DATA_BYTE_WIDTH{1'b1}};  // Full mask
            @(posedge mm_clk);
            mm_wen <= 1'b0;
            $display("  Written data[%0d] = %h to addr %h", i, test_data[i], 29'h1000 + (i * 32));
        end

        // Step 2: Issue multiple read requests with mm_rready = 0
        $display("Step 2: Issuing multiple read requests with mm_rready = 0");
        mm_rready <= 1'b0;  // Keep mm_rready low

        for (int i = 0; i < 4; i++) begin
            @(posedge mm_clk);
            mm_ren <= 1'b1;
            mm_raddr <= 29'h1000 + (i * 32);
            @(posedge mm_clk);
            mm_ren <= 1'b0;
            $display("  Issued read request %0d for addr %h", i, 29'h1000 + (i * 32));

            // 等待几个周期，确保数据准备好但不输出
            repeat(3) @(posedge mm_clk);
        end

        // Step 3: 验证没有数据输出
        $display("Step 3: Verifying no data output when mm_rready = 0");
        repeat(100000) @(posedge mm_clk);
        if (mm_rvalid) begin
            $display("ERROR: mm_rvalid should not be asserted when mm_rready = 0");
        end else begin
            $display("PASS: mm_rvalid correctly remains low when mm_rready = 0");
        end

        // Step 4: Assert mm_rready and receive all pending data
        $display("Step 4: Asserting mm_rready and receiving all pending data");
        received_count = 0;

        // Set rready high to start receiving data
        mm_rready <= 1'b1;

        // Loop until we have received all 4 data items, with a timeout

        i = 0;
        while(1) begin // 50 cycles timeout
            @(posedge mm_clk);
            if (mm_rvalid) begin
                if (received_count < 4) begin
                    received_data[received_count] = mm_rdata;
                    $display("  Received data[%0d] = %h", received_count, mm_rdata);
                    received_count++;
                end else begin
                    $display("ERROR: Received more than 4 data items unexpectedly.");
                end
            end
            if (i == 2) begin
                mm_rready <= 1'b0;
            end else begin
                mm_rready <= 1'b1;
            end
            i++;
            if (received_count == 4) break; // Exit loop when all data is received
        end

        // De-assert rready after receiving data
        @(posedge mm_clk);
        mm_rready <= 1'b0;

        // Check if we timed out
        if (received_count < 4) begin
            $display("ERROR: Timeout. Received only %0d out of 4 data items.", received_count);
        end

        // Step 5: 验证接收到的数据
        $display("Step 5: Verifying received data");
        if (received_count != 4) begin
            $display("ERROR: Expected 4 data items, received %0d", received_count);
        end else begin
            $display("PASS: Received expected number of data items");

            // 验证数据内容（注意：由于FIFO的特性，数据应该按顺序返回）
            for (int i = 0; i < 4; i++) begin
                if (received_data[i] == test_data[i]) begin
                    $display("PASS: Data[%0d] matches expected value", i);
                end else begin
                    $display("ERROR: Data[%0d] mismatch. Expected: %h, Got: %h",
                            i, test_data[i], received_data[i]);
                end
            end
        end

        // Step 6: 测试连续读取（mm_rready一直为1）
        $display("Step 6: Testing continuous read with mm_rready = 1");
        mm_rready <= 1'b1;

        cont_count = 0;
        fork
            begin
                @(posedge mm_clk);
                mm_ren <= 1'b1;
                mm_raddr <= 29'h1000;
                @(posedge mm_clk);
                mm_raddr <= 29'h1020;
                @(posedge mm_clk);
                mm_raddr <= 29'h1040;
                @(posedge mm_clk);
                mm_raddr <= 29'h1060;
                @(posedge mm_clk);
                mm_ren <= 1'b0;
            end

            begin
                // 等待数据
                while(1) begin
                    @(posedge mm_clk);
                    if (mm_rvalid) begin
                        if (mm_rdata == test_data[cont_count]) begin
                            $display("PASS: Continuous read %0d works correctly", cont_count);
                        end else begin
                            $display("ERROR: %0d Continuous read failed. Expected: %h, Got: %h",
                                    cont_count, test_data[cont_count], mm_rdata);
                        end
                        cont_count++;
                    end
                    if (cont_count == 4) break;
                end
            end
        join

        @(posedge mm_clk);
        mm_rready <= 1'b0;  // Clean up

    endtask

endmodule

# HBM3 MM BFM mm_rready Implementation

## 概述
在hbm3_mm_bfm接口上增加了一个读控制信号`mm_rready`，当`mm_rready`有效时，才能返回read data。此功能在blocking和nonblock模式下都已实现。

**重要更新**：为了解决多个读请求在`mm_rready=0`时的数据丢失问题，新增了专门的RREADY FIFO来缓存因反压而无法立即输出的读数据。

## 修改内容

### 1. 接口信号定义
在`HBM3/BFM/src/hbm3_mm_bfm.sv`中，`mm_rready`信号已经在接口中定义：
```systemverilog
input mm_rready,
```

### 2. RREADY FIFO设计
为了解决多个读请求在`mm_rready=0`时的数据丢失问题，新增了专门的RREADY FIFO：

```systemverilog
// mm_rready backpressure FIFO - 用于存储因mm_rready=0而无法输出的数据
localparam RREADY_FIFO_AWIDTH = $clog2(MAX_OUTSTANDING_READS);
localparam RREADY_FIFO_DEPTH = MAX_OUTSTANDING_READS;
reg [DATA_WIDTH-1:0] rready_fifo [0:RREADY_FIFO_DEPTH-1];
bit [RREADY_FIFO_AWIDTH:0] rready_fifo_rptr;
bit [RREADY_FIFO_AWIDTH:0] rready_fifo_wptr;
```

### 3. Nonblocking模式修改
在nonblocking模式下，实现了三级数据路径优先级：

**新的输出控制逻辑：**
```systemverilog
// 输出控制：优先级为 RREADY FIFO > Low-latency > Normal FIFO
if (!rready_fifo_empty && mm_rready) begin
    // 从RREADY FIFO输出数据
    mm_rdata <= rready_fifo[rready_fifo_rptr[RREADY_FIFO_AWIDTH-1:0]];
    mm_rvalid <= 1'b1;
    rready_fifo_rptr <= rready_fifo_rptr + 1;
end else if (ll_rvalid_hold && mm_rready) begin
    // 直接从low-latency path输出
    mm_rdata <= ll_rdata_hold;
    mm_rvalid <= 1'b1;
end else if (!fifo_empty && mm_rready) begin
    // 从normal FIFO输出
    mm_rdata <= read_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    mm_rvalid <= 1'b1;
    fifo_rptr <= fifo_rptr + 1;
end
```

**反压处理逻辑：**
```systemverilog
// 处理数据到RREADY FIFO的写入
// Low-latency path has priority for writing to RREADY FIFO
if (ll_rvalid_hold && !mm_rready && !rready_fifo_full) begin
    // 将low-latency数据写入RREADY FIFO
    rready_fifo[rready_fifo_wptr[RREADY_FIFO_AWIDTH-1:0]] <= ll_rdata_hold;
    rready_fifo_wptr <= rready_fifo_wptr + 1;
end else if (!fifo_empty && !mm_rready && !rready_fifo_full) begin
    // 将FIFO数据写入RREADY FIFO
    rready_fifo[rready_fifo_wptr[RREADY_FIFO_AWIDTH-1:0]] <= read_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    rready_fifo_wptr <= rready_fifo_wptr + 1;
    fifo_rptr <= fifo_rptr + 1; // 从原FIFO中移除数据
end
```

### 4. Blocking模式修改
在blocking模式下，同样实现了RREADY FIFO支持：

**新的输出控制逻辑：**
```systemverilog
// 输出控制：优先级为 RREADY FIFO > 当前读数据
mm_rvalid <= 1'b0;  // 默认无效
if (!rready_fifo_empty && mm_rready) begin
    // 从RREADY FIFO输出数据
    mm_rdata <= rready_fifo[rready_fifo_rptr[RREADY_FIFO_AWIDTH-1:0]];
    mm_rvalid <= 1'b1;
    rready_fifo_rptr <= rready_fifo_rptr + 1;
end else if (read_valid && mm_rready) begin
    // 直接输出当前读数据
    mm_rdata <= read_data_hold;
    mm_rvalid <= 1'b1;
end
```

**反压处理逻辑：**
```systemverilog
// 处理数据到RREADY FIFO的写入
if (read_valid && !mm_rready && !rready_fifo_full) begin
    // 将读数据写入RREADY FIFO
    rready_fifo[rready_fifo_wptr[RREADY_FIFO_AWIDTH-1:0]] <= read_data_hold;
    rready_fifo_wptr <= rready_fifo_wptr + 1;
end
```

### 4. 测试文件修改
更新了blocking和nonblocking测试文件，添加了`mm_rready`信号的声明、连接和初始化：

#### Blocking测试文件 (`HBM3/BFM/tests/blocking/src/sv/hbm3_mm_tb.sv`)
- 添加信号声明：`logic [NUM_PCS-1:0] mm_rready;`
- 添加端口连接：`.mm_rready(mm_rready[i]),`
- 添加初始化：`mm_rready = '1;  // 默认设置为高电平，允许读数据返回`

#### Nonblocking测试文件 (`HBM3/BFM/tests/nonblocking/src/sv/hbm3_mm_tb.sv`)
- 添加信号声明：`logic [NUM_PCS-1:0] mm_rready;`
- 添加端口连接：`.mm_rready(mm_rready[i]),`
- 添加初始化：`mm_rready = '1;  // 默认设置为高电平，允许读数据返回`

### 5. 新增测试文件
创建了专门的测试文件`HBM3/BFM/tests/mm_rready_test.sv`来验证`mm_rready`功能：
- 测试当`mm_rready = 0`时，`mm_rvalid`不会被断言
- 测试当`mm_rready = 1`时，读数据能正常返回
- 测试多次读取周期中的`mm_rready`控制

## 功能说明

### mm_rready信号行为
- **mm_rready = 0**: BFM不会输出读数据，数据被缓存到RREADY FIFO中，`mm_rvalid`保持为0
- **mm_rready = 1**: BFM优先从RREADY FIFO输出缓存的数据，然后输出新的读数据

### RREADY FIFO特性
- **深度**: 等于`MAX_OUTSTANDING_READS`参数，确保能缓存所有可能的未完成读请求
- **优先级**: RREADY FIFO中的数据具有最高输出优先级，确保数据按顺序返回
- **防溢出**: 当RREADY FIFO满时，新的读数据会被丢弃（但这种情况在正常使用中不应发生）

### 数据路径优先级
1. **Nonblocking模式**:
   - 输出优先级：RREADY FIFO > Low-latency path > Normal FIFO
   - 写入RREADY FIFO优先级：Low-latency path > Normal FIFO

2. **Blocking模式**:
   - 输出优先级：RREADY FIFO > 当前读数据
   - 所有因`mm_rready=0`无法输出的数据都会被缓存

### 数据完整性保证
- **无数据丢失**: 所有读请求的数据都会被正确缓存和返回
- **顺序保证**: 数据按照读请求的顺序返回
- **反压支持**: 支持上游的流控制需求

## 向后兼容性
- 现有的测试文件已更新，默认将`mm_rready`设置为高电平
- 对于不使用`mm_rready`控制的应用，只需将信号连接到高电平即可保持原有行为

## 验证建议
1. 运行现有的blocking和nonblocking测试，确保基本功能不受影响
2. 运行新的`mm_rready_test.sv`测试，验证读控制功能
3. 在实际应用中测试`mm_rready`的动态控制

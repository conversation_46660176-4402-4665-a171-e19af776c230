# Non-Blocking Mode with Read Latency Fallback

## 1. Requirement

The primary goal was to enhance the non-blocking mode of the HBM3 BFM to support a `read_latency` constraint. This constraint, provided as an input, specifies the maximum number of clock cycles (`mm_clk`) allowed between a read request (`mm_ren`) being asserted and its corresponding data being returned (`mm_rvalid`).

The existing non-blocking DPI-C calls improve simulation performance by not halting the hardware clock. However, this creates a timing challenge, as the software side's processing time is variable and could exceed the `read_latency` required by the hardware.

## 2. Design & Implementation

A hybrid approach was implemented to satisfy this requirement. The BFM primarily uses the existing non-blocking polling mechanism but includes a fallback to a blocking call if a timeout is imminent.

### 2.1. Core Implementation Details

- **Timeout Detection:** A 32-bit cycle counter (`cycle_counter`) increments on every `mm_clk`. When a read request is issued, its start time (the current `cycle_counter` value) is recorded. An `always` block continuously monitors the oldest pending request. If `cycle_counter - start_cycle >= read_latency`, a timeout is triggered.

- **Fallback Mechanism:** Upon a timeout, the BFM immediately calls a new **blocking** DPI-C function, `h2s_hbm3_read_data_blocking`. This call halts the hardware clock and forces the C++ model to return the required data immediately, thus guaranteeing the latency requirement is met.

- **Request Tracking:** To manage multiple in-flight requests, a synthesizable FIFO was implemented in SystemVerilog. This FIFO stores the start cycle of each pending read request.

- **C++ Model:** The C++ model uses a standard `std::queue` to store incoming read requests, ensuring they are processed in the correct order. The new blocking function simply retrieves the oldest request from the front of this queue. This strict ordering on both the SV and C++ sides ensures they remain synchronized.

## 3. Emulator-Imposed Limitations & Design Evolution

The final design was heavily influenced by the strict constraints of the target emulator environment. Several initial design ideas were discarded due to these limitations:

1.  **No Associative Arrays:** An initial proposal to use a SystemVerilog associative array (`int unsigned pending_reads[int unsigned]`) to track requests by a unique transaction ID (TID) was rejected. Associative arrays are not synthesizable and are forbidden in the emulator environment.
    *   **Solution:** A synthesizable FIFO, implemented with a fixed-size array and counters (`pending_req_count`, `pending_resp_count`), was used instead.

2.  **Strictly In-Order Requests:** The initial design assumed that read responses could potentially return out-of-order, making a TID-based tracking system necessary. However, it was clarified that the requirement is for **strictly in-order** processing (First-In, First-Out).
    *   **Solution:** This simplification allowed for the removal of the TID mechanism and the adoption of a much simpler FIFO structure for tracking.

3.  **No Real-World Timers:** The concept of using `std::chrono` or other wall-clock timers was incorrect. All timing within the BFM must be based on the hardware clock (`mm_clk`).
    *   **Solution:** A simple `reg [31:0] cycle_counter` was implemented in the BFM to handle all timing and latency calculations.

4.  **No Concurrent Writes to Registers:** A design that used a single `pending_count` register, which was written to by both an `always` block (for requests) and a DPI `function` (for responses), was identified as a potential race condition. In a synthesizable context, this is illegal.
    *   **Solution:** The single counter was replaced by two separate counters: `pending_req_count` (incremented only when a request is sent) and `pending_resp_count` (incremented only when a response is received). The number of pending reads is then safely calculated as `pending_req_count - pending_resp_count`.
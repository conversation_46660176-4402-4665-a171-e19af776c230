# RREADY FIFO 设计说明

## 问题背景

在原始实现中，当`mm_rready=0`时，如果有多个读请求返回数据，会出现数据覆盖问题：

### Nonblocking模式问题
- `ll_rdata_hold`只能保存一个low-latency数据
- 连续的low-latency读请求会导致数据丢失

### Blocking模式问题  
- `read_data_hold`只能保存一个读数据
- 连续的读请求会导致数据覆盖

## 解决方案：RREADY FIFO

### 设计原理
```
读请求 → 数据准备好 → mm_rready检查 → 输出路径选择
                           ↓
                      mm_rready=0
                           ↓
                    写入RREADY FIFO
                           ↓
                    等待mm_rready=1
                           ↓
                    从RREADY FIFO输出
```

### FIFO参数
- **深度**: `MAX_OUTSTANDING_READS` (通常为16)
- **宽度**: `DATA_WIDTH` (通常为256位)
- **地址宽度**: `$clog2(MAX_OUTSTANDING_READS)`

## Nonblocking模式数据流

### 数据路径优先级
1. **输出优先级** (从高到低):
   - RREADY FIFO (缓存的反压数据)
   - Low-latency path (直接路径)
   - Normal FIFO (非阻塞FIFO)

2. **写入RREADY FIFO优先级** (从高到低):
   - Low-latency path数据
   - Normal FIFO数据

### 状态转换
```
mm_rready=1, 有RREADY FIFO数据 → 输出RREADY FIFO数据
mm_rready=1, 无RREADY FIFO数据, 有LL数据 → 输出LL数据
mm_rready=1, 无RREADY FIFO数据, 无LL数据, 有FIFO数据 → 输出FIFO数据
mm_rready=0, 有LL数据 → LL数据写入RREADY FIFO
mm_rready=0, 有FIFO数据 → FIFO数据写入RREADY FIFO
```

## Blocking模式数据流

### 数据路径优先级
1. **输出优先级** (从高到低):
   - RREADY FIFO (缓存的反压数据)
   - 当前读数据 (read_data_hold)

### 状态转换
```
mm_rready=1, 有RREADY FIFO数据 → 输出RREADY FIFO数据
mm_rready=1, 无RREADY FIFO数据, 有当前读数据 → 输出当前读数据
mm_rready=0, 有当前读数据 → 当前读数据写入RREADY FIFO
```

## 关键特性

### 1. 数据完整性
- 所有读请求的数据都会被正确处理
- 不会因为`mm_rready=0`而丢失数据

### 2. 顺序保证
- FIFO结构确保数据按照到达顺序输出
- 先进先出的原则保持数据时序

### 3. 反压支持
- 支持上游模块的流控制需求
- 允许下游模块控制数据接收速率

### 4. 资源优化
- FIFO深度等于最大未完成读请求数
- 避免过度分配存储资源

## 使用场景

### 典型应用
1. **流控制**: 下游模块需要控制数据接收速率
2. **缓冲管理**: 处理突发的数据流量
3. **时序对齐**: 与其他数据流进行时序同步

### 配置建议
- 确保`MAX_OUTSTANDING_READS`参数足够大以处理预期的读请求数量
- 监控RREADY FIFO的使用情况，避免溢出

## 验证要点

### 功能验证
1. 多个读请求在`mm_rready=0`时的数据缓存
2. `mm_rready=1`时数据的正确输出顺序
3. FIFO满时的处理行为
4. 不同数据路径的优先级验证

### 性能验证
1. 最大吞吐量测试
2. 延迟特性分析
3. 资源使用效率评估
